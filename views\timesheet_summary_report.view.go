package views

import (
	"encoding/json"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

type TimesheetSummaryReportProjectTiming struct {
	ProjectCode *string `json:"project_code"`
	ProjectName *string `json:"project_name"`
	SgaName     *string `json:"sga_name"`
	Type        string  `json:"type"`
	TotalTiming float64 `json:"total_timing"`
}

type TimesheetSummaryReportView struct {
	*models.User
	TotalTiming         float64                               `json:"total_timing"`
	TotalProjectTiming  float64                               `json:"total_project_timing"`
	TotalLeaveTiming    float64                               `json:"total_leave_timing"`
	TotalSgaTiming      float64                               `json:"total_sga_timing"`
	TotalInternalTiming float64                               `json:"total_internal_timing"`
	TotalExternalTiming float64                               `json:"total_external_timing"`
	TotalOtTiming       float64                               `json:"total_ot_timing"`
	Timings             []TimesheetSummaryReportProjectTiming `json:"timings"`
}

func (v TimesheetSummaryReportView) MarshalJSON() ([]byte, error) {
	// Create a map to hold all the data
	result := make(map[string]interface{})

	// Add user data if available
	if v.User != nil {
		// First marshal the user to get its JSON representation
		userBytes, err := json.Marshal(v.User)
		if err != nil {
			return nil, err
		}

		// Unmarshal into our result map to get all user fields
		if err := json.Unmarshal(userBytes, &result); err != nil {
			return nil, err
		}
	}

	// Add timesheet summary fields (these will override any conflicting user fields)
	result["total_timing"] = v.TotalTiming
	result["total_project_timing"] = v.TotalProjectTiming
	result["total_leave_timing"] = v.TotalLeaveTiming
	result["total_sga_timing"] = v.TotalSgaTiming
	result["total_internal_timing"] = v.TotalInternalTiming
	result["total_external_timing"] = v.TotalExternalTiming
	result["total_ot_timing"] = v.TotalOtTiming
	result["timings"] = v.Timings

	return json.Marshal(result)
}
